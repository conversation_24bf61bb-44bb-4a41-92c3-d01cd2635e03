# Standard library imports
import os
import asyncio
import logging
import traceback
import io
import tempfile
import concurrent.futures
import time
import uuid
import hashlib
from datetime import datetime
from typing import Dict, Any, Tuple, List
from pathlib import Path
from urllib.parse import urlparse

# Third-party imports
import boto3
import PyPDF2
from botocore.exceptions import ClientError

# Local imports
from app.core.configuration import settings

# Constants
DEFAULT_MAX_WORKERS = 6
DEFAULT_POLL_INTERVAL = 5
DEFAULT_MAX_WAIT_TIME = 300
TEXTRACT_MAX_FILE_SIZE = 10 * 1024 * 1024  # 10 MB limit for sync operations

# Configure logging
logger = logging.getLogger(__name__)


def parse_s3_uri(s3_uri: str) -> Tuple[str, str]:
    """
    Parse S3 URI into bucket name and object key.

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key

    Returns:
        tuple: (bucket_name, object_key)

    Raises:
        ValueError: If URI format is invalid
    """
    try:
        parsed = urlparse(s3_uri)
        if parsed.scheme != 's3':
            raise ValueError(f"Invalid S3 URI scheme. Expected 's3', got '{parsed.scheme}'")

        bucket_name = parsed.netloc
        object_key = parsed.path.lstrip('/')

        if not bucket_name:
            raise ValueError("Bucket name is missing from S3 URI")
        if not object_key:
            raise ValueError("Object key is missing from S3 URI")

        return bucket_name, object_key

    except Exception as e:
        raise ValueError(f"Failed to parse S3 URI '{s3_uri}': {str(e)}")


class TextractProcessor:
    """
    AWS Textract processor for document analysis and text extraction.
    """

    def __init__(self, s3_client=None):
        """
        Initialize Textract processor with credentials from settings.
        """
        logger.info("Initializing TextractProcessor...")

        try:
            # Load AWS credentials from settings
            aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
            aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
            aws_region = settings.AWS_REGION_EXTRACTION

            self.textract_client = boto3.client(
                'textract',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            if s3_client:
                self.s3_client = s3_client
            else:
                self.s3_client = boto3.client(
                    's3',
                    region_name=aws_region,
                    aws_access_key_id=aws_access_key_id,
                    aws_secret_access_key=aws_secret_access_key
                )

            self.region = aws_region

            # Supported file formats for sync operations
            self.supported_formats = {'.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.tif'}
            self.max_file_size = TEXTRACT_MAX_FILE_SIZE

            # Validate AWS credentials are available
            if not aws_access_key_id and not aws_secret_access_key:
                logger.warning("AWS credentials not provided - using default credential chain")

        except Exception as e:
            logger.error(f"Failed to initialize TextractProcessor: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise

    def _start_async_textract_job(self, bucket_name: str, object_key: str) -> str:
        """
        Start an async Textract document text detection job.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            Job ID for the started job
        """
        response = self.textract_client.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': object_key
                }
            }
        )
        return response['JobId']

    def _poll_textract_job(self, job_id: str, max_wait_time: int = DEFAULT_MAX_WAIT_TIME, poll_interval: int = DEFAULT_POLL_INTERVAL) -> Dict:
        """
        Poll a Textract job until completion or timeout.

        Args:
            job_id: Textract job ID to poll
            max_wait_time: Maximum time to wait in seconds (default: 300)
            poll_interval: Polling interval in seconds (default: 5)

        Returns:
            Textract response when job completes

        Raises:
            Exception: If job fails or times out
        """
        elapsed_time = 0
        poll_count = 0

        while elapsed_time < max_wait_time:
            poll_count += 1

            result = self.textract_client.get_document_text_detection(JobId=job_id)
            status = result['JobStatus']

            logger.info(f"    Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time}s)")

            if status == 'SUCCEEDED':
                return result
            elif status == 'FAILED':
                error_msg = result.get('StatusMessage', 'Unknown error')
                raise Exception(f"Textract job {job_id} failed: {error_msg}")
            elif status in ['IN_PROGRESS']:
                time.sleep(poll_interval)
                elapsed_time += poll_interval
            else:
                raise Exception(f"Unexpected job status: {status}")

        raise Exception(f"Textract job {job_id} timed out after {max_wait_time} seconds")

    def _get_file_metadata(self, s3_uri: str) -> Dict[str, Any]:
        """
        Get file metadata from S3.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: File metadata including size and content type
        """
        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)
            response = self.s3_client.head_object(Bucket=bucket_name, Key=object_key)
            return {
                'size': response['ContentLength'],
                'content_type': response.get('ContentType', ''),
                'last_modified': response['LastModified']
            }
        except ClientError as e:
            logger.error(f"Failed to get file metadata: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise

    def _get_file_name(self, s3_uri: str) -> str:
        """
        Extract file name from S3 URI.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            str: File name
        """
        try:
            _, object_key = parse_s3_uri(s3_uri)
            return Path(object_key).name
        except Exception as e:
            logger.error(f"Failed to extract file name: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise

    def _count_pdf_pages(self, s3_uri: str) -> int:
        """
        Count pages in a PDF file from S3.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            int: Number of pages in PDF, or 1 if unable to determine
        """
        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)
            # Download PDF content
            response = self.s3_client.get_object(Bucket=bucket_name, Key=object_key)
            pdf_content = response['Body'].read()

            # Create PDF reader
            pdf_stream = io.BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_stream)

            page_count = len(pdf_reader.pages)
            return page_count

        except Exception as e:
            logger.warning(f"Failed to count PDF pages: {str(e)}")
            logger.debug("Assuming 1 page for sync processing")
            return 1

    def _should_use_sync_method(self, s3_uri: str) -> Tuple[bool, str]:
        """
        Determine if sync method should be used based on file criteria.

        Criteria for sync method:
        - File extension is supported (.pdf, .jpg, .jpeg, .png, .tiff, .tif)
        - File size is under 10 MB limit
        - For PDF: single page only (calculated, not assumed)
        - For TIFF: single page assumed

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            tuple: (can_use_sync, reason)
        """
        try:
            _, object_key = parse_s3_uri(s3_uri)

            # Get file metadata
            metadata = self._get_file_metadata(s3_uri)
            file_size = metadata['size']
            file_size_mb = file_size / (1024 * 1024)

            # Check file extension
            file_ext = Path(object_key).suffix.lower()
            if file_ext not in self.supported_formats:
                reason = f"Unsupported format: {file_ext}. Supported: {self.supported_formats}"
                logger.info(f"    Decision: ASYNC - {reason}")
                return False, reason

            # Check file size
            if file_size > self.max_file_size:
                max_size_mb = self.max_file_size / (1024 * 1024)
                reason = f"File size ({file_size_mb:.2f} MB) exceeds {max_size_mb:.0f} MB limit"
                logger.info(f"    Size: {file_size_mb:.2f} MB")
                logger.info(f"    Decision: ASYNC - {reason}")
                return False, reason

            # For PDF, count actual pages
            if file_ext == '.pdf':
                page_count = self._count_pdf_pages(s3_uri)
                if page_count > 1:
                    reason = f"PDF has {page_count} pages (sync supports single page only)"
                    logger.info(f"    Decision: ASYNC - {reason}")
                    return False, reason

            # For TIFF, assume single page (could be enhanced later)
            elif file_ext in ['.tiff', '.tif']:
                logger.info(f"    TIFF detected. Assuming single page for sync processing.")

            reason = f"File suitable for synchronous processing"
            logger.info(f"    Size: {file_size_mb:.2f} MB")
            logger.info(f"    Format: {file_ext} (supported)")
            logger.info(f"    Decision: SYNC - {reason}")

            return True, reason

        except Exception as e:
            reason = f"Failed to analyze file for sync/async decision: {str(e)}"
            logger.warning(f"⚠️  {reason}")
            logger.warning(f"    Traceback: {traceback.format_exc()}")
            logger.warning("    Defaulting to ASYNC method")
            return False, reason

    def analyze_document_sync(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using synchronous Textract detect_document_text.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results
        """

        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)

            # Use detect_document_text for sync processing
            response = self.textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )

            return response

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ SYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during SYNC Textract analysis: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    async def analyze_document_async(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for text extraction only (no tables/forms).

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting ASYNC Textract text detection...")

        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)

            # Start async Textract job
            job_id = self._start_async_textract_job(bucket_name, object_key)
            logger.info(f"✅ Textract text detection started successfully")
            logger.info(f"    Job ID: {job_id}")

            # Poll for completion
            logger.info("⏳ Waiting for Textract text detection to complete...")
            start_time = datetime.now()

            result = self._poll_textract_job(job_id)

            elapsed_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"✅ ASYNC Textract text detection completed successfully!")
            logger.info(f"    Total processing time: {elapsed_time:.1f} seconds")

            return result

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ ASYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during ASYNC Textract text detection: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    def _convert_textract_to_structured_format(self, textract_response: Dict[str, Any]) -> str:
        """
        Convert Textract response to structured text format with coordinates.

        Args:
            textract_response: Textract API response

        Returns:
            str: Structured text with coordinates in CSV format
        """
        try:
            blocks = textract_response.get('Blocks', [])

            # Extract text blocks with coordinates in the required format
            text_lines = []

            # Process LINE blocks for text with coordinates (most efficient)
            line_blocks = [block for block in blocks if block['BlockType'] == 'LINE']

            if line_blocks:
                for block in line_blocks:
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")
            else:
                # If no LINE blocks found (sync response), use WORD blocks
                logger.info("No LINE blocks found, using WORD blocks for text extraction")
                word_blocks = [block for block in blocks if block['BlockType'] == 'WORD']

                for block in word_blocks:
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # Build the structured text format (text only, no tables for speed)
            structured_text_parts = []

            # Add header
            structured_text_parts.append("=== TEXT WITH COORDINATES ===")
            structured_text_parts.append("text, x1, y1, x2, y2")

            # Add text lines
            for line in text_lines:
                structured_text_parts.append(line)

            structured_text = "\n".join(structured_text_parts)

            logger.info(f"    Extracted {len(text_lines)} text lines with coordinates")
            return structured_text

        except Exception as e:
            logger.error(f"❌ Error converting Textract response to structured format: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    
    async def process_document(self, s3_uri: str) -> Dict[str, Any]:
        """
        Complete Textract processing: tries sync first, then async if needed.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Complete Textract processing results
        """
        # Input validation
        if not s3_uri or not isinstance(s3_uri, str):
            raise ValueError("s3_uri must be a non-empty string")

        if not s3_uri.startswith('s3://'):
            raise ValueError("s3_uri must start with 's3://'")

        start_time = datetime.now()
        processing_method = "UNKNOWN"

        try:
            # Determine processing method based on file criteria
            should_use_sync, reason = self._should_use_sync_method(s3_uri)

            file_name = self._get_file_name(s3_uri)

            if should_use_sync:
                processing_method = "SYNC"
                try:
                    # Try sync method first
                    textract_result = self.analyze_document_sync(s3_uri)

                except Exception as sync_error:
                    logger.warning(f"⚠️  SYNC processing failed: {str(sync_error)}")
                    logger.warning(f"    Sync failure reason: {reason}")
                    logger.warning(f"    Traceback: {traceback.format_exc()}")
                    logger.info("🔄 Falling back to ASYNC processing...")
                    processing_method = "ASYNC_FALLBACK"
                    textract_result = await self.analyze_document_async(s3_uri)
                    logger.info("✅ ASYNC fallback processing successful!")
            else:
                logger.info(f"🚀 Using ASYNC processing directly... Reason: {reason}")
                processing_method = "ASYNC"
                textract_result = await self.analyze_document_async(s3_uri)
                logger.info("✅ ASYNC processing successful!")

            # Extract structured text with coordinates
            structured_text = self._convert_textract_to_structured_format(textract_result)

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "textract_metadata": {
                    "s3_uri": s3_uri,
                    "file_name": file_name,
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "processing_method": processing_method,
                    "aws_region": self.region
                },
                "textract_response_object": textract_result,
                "structured_text": structured_text
            }

            logger.info("✅ Textract processing completed successfully!")
            logger.info(f"    Total processing time for Textract: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Textract processing failed!")
            logger.error(f"    Processing method attempted: {processing_method}")
            logger.error(f"    Error: {str(e)}")
            logger.error(f"    Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    def split_pdf(self, pdf_path: str, output_dir: str) -> List[str]:
        """
        Split a PDF file into individual pages.

        Args:
            pdf_path: Path to the PDF file
            output_dir: Directory to save individual pages

        Returns:
            List of paths to individual page files
        """
        page_files = []
        pdf_name = Path(pdf_path).stem

        # Create a hash of the PDF path to ensure unique filenames
        pdf_hash = hashlib.md5(pdf_path.encode()).hexdigest()[:8]

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            total_pages = len(pdf_reader.pages)

            logging.info(f"Splitting PDF {pdf_name} into {total_pages} pages")

            for page_num in range(total_pages):
                # Create a new PDF writer for each page
                pdf_writer = PyPDF2.PdfWriter()
                pdf_writer.add_page(pdf_reader.pages[page_num])

                # Save individual page with unique naming to prevent conflicts
                page_filename = f"{pdf_name}_{pdf_hash}_page_{page_num + 1:03d}.pdf"
                page_path = os.path.join(output_dir, page_filename)

                with open(page_path, 'wb') as output_file:
                    pdf_writer.write(output_file)

                page_files.append(page_path)
                logging.debug(f"Created page file: {page_filename}")

        return page_files

    def process_page_with_textract_response(self, page_path: str, bucket_name: str, temp_prefix: str) -> Tuple[int, str, Dict]:
        """
        Process a single PDF page with AWS Textract and return both text and response object.

        Args:
            page_path: Path to the PDF page file
            bucket_name: S3 bucket name for temporary uploads
            temp_prefix: S3 prefix for temporary files

        Returns:
            Tuple of (page_number, extracted_text, textract_response)
        """
        page_filename = os.path.basename(page_path)

        # Extract page number from filename
        if '_page_' in page_filename:
            page_part = page_filename.split('_page_')[1].split('.')[0]
            page_num = int(page_part)
        else:
            page_num = 1

        s3_key = None  # Track if we uploaded to S3

        try:
            # Try sync method first - read file content
            with open(page_path, 'rb') as file:
                file_content = file.read()

            try:
                response = self.textract_client.detect_document_text(
                    Document={'Bytes': file_content}
                )
                logging.debug(f"Sync method succeeded for page {page_num}")

            except Exception as sync_error:
                logging.warning(f"Sync method failed for page {page_num}, trying async method: {sync_error}")

                # Upload to S3 only when async method is needed with unique key
                timestamp = int(time.time() * 1000)  # millisecond timestamp
                unique_id = uuid.uuid4().hex[:8]
                s3_key = f"{temp_prefix}/{timestamp}_{unique_id}_{page_filename}"
                self.s3_client.upload_file(page_path, bucket_name, s3_key)
                logging.info(f"Uploaded {page_filename} to S3 with unique key: {s3_key}")

                # Fallback to async method
                try:
                    # Start async job
                    job_id = self._start_async_textract_job(bucket_name, s3_key)
                    logging.info(f"Started async Textract job {job_id} for page {page_num}")

                    # Poll for completion
                    response = self._poll_textract_job(job_id, max_wait_time=120, poll_interval=2)
                    logging.info(f"Async method succeeded for page {page_num}")

                except Exception as async_error:
                    logging.error(f"Both sync and async methods failed for page {page_num}: {async_error}")
                    return page_num, f"Error processing page: {async_error}", {}

            # Extract text from response
            text_lines = []
            for block in response.get('Blocks', []):
                if block['BlockType'] == 'LINE':
                    text_lines.append(block['Text'])

            extracted_text = '\n'.join(text_lines)

            # Add validation and metadata for debugging
            char_count = len(extracted_text)
            line_count = len(text_lines)

            logging.info(f"Page {page_num}: Extracted {char_count} characters, {line_count} lines from {page_filename}")

            validated_text = f"{extracted_text}"

            # Clean up S3 file only if it was uploaded
            if s3_key:
                try:
                    self.s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
                    logging.debug(f"Cleaned up S3 file: {s3_key}")
                except Exception:
                    pass  # Ignore cleanup errors

            return page_num, validated_text, response

        except Exception as e:
            logging.error(f"Error processing page {page_path}: {e}")
            return 0, f"Error processing page: {e}", {}

    def process_s3_image_with_textract_response(self, bucket_name: str, object_key: str) -> Tuple[str, Dict]:
        """
        Process an image file from S3 with AWS Textract and return both text and response object.

        Args:
            bucket_name: S3 bucket name containing the image
            object_key: S3 object key for the image

        Returns:
            Tuple of (extracted_text, textract_response_object)
        """
        try:
            logging.debug(f"Processing S3 image: s3://{bucket_name}/{object_key}")

            try:
                # Try sync method first - use S3 document location
                response = self.textract_client.detect_document_text(
                    Document={
                        'S3Object': {
                            'Bucket': bucket_name,
                            'Name': object_key
                        }
                    }
                )
                logging.debug(f"Sync method succeeded for S3 image {object_key}")

            except Exception as sync_error:
                logging.warning(f"Sync method failed for S3 image {object_key}, trying async method: {sync_error}")

                # Fallback to async method
                try:
                    # Start async job
                    job_id = self._start_async_textract_job(bucket_name, object_key)
                    logging.info(f"Started async Textract job {job_id} for S3 image {object_key}")

                    # Poll for completion
                    response = self._poll_textract_job(job_id, max_wait_time=120, poll_interval=2)
                    logging.info(f"Async method succeeded for S3 image {object_key}")

                except Exception as async_error:
                    logging.error(f"Both sync and async methods failed for S3 image {object_key}: {async_error}")
                    return f"Error processing image: {async_error}", {}

            # Extract text from response
            text_lines = []
            for block in response.get('Blocks', []):
                if block['BlockType'] == 'LINE':
                    text_lines.append(block['Text'])

            extracted_text = '\n'.join(text_lines)

            # Add validation and metadata for debugging
            char_count = len(extracted_text)
            line_count = len(text_lines)

            logging.info(f"S3 Image {object_key}: Extracted {char_count} characters, {line_count} lines")

            return extracted_text, response

        except Exception as e:
            logging.error(f"Error processing S3 image s3://{bucket_name}/{object_key}: {e}")
            return f"Error processing image: {e}", {}

    def process_s3_pdf_with_responses(self, bucket_name: str, object_key: str, temp_bucket_name: str, temp_prefix: str) -> Tuple[str, List[Dict]]:
        """
        Process a PDF file from S3 by downloading, splitting, and processing in parallel.

        Args:
            bucket_name: S3 bucket name containing the PDF
            object_key: S3 object key for the PDF
            temp_bucket_name: S3 bucket name for temporary uploads
            temp_prefix: S3 prefix for temporary files

        Returns:
            Tuple of (combined_text, list_of_textract_responses)
        """
        try:
            # Download PDF to temporary directory and process
            with tempfile.TemporaryDirectory() as temp_dir:
                # Download PDF from S3
                pdf_filename = os.path.basename(object_key)
                local_pdf_path = os.path.join(temp_dir, pdf_filename)

                logging.info(f"Downloading PDF from S3 to {local_pdf_path}")
                self.s3_client.download_file(bucket_name, object_key, local_pdf_path)

                # Split PDF into individual pages
                logging.info("Splitting PDF into individual pages...")
                page_files = self.split_pdf(local_pdf_path, temp_dir)
                logging.info(f"Split PDF into {len(page_files)} pages")

                # Process pages in parallel
                logging.info("Processing pages with Textract in parallel...")
                combined_text, textract_responses = self.process_pdf_pages_parallel_with_responses(
                    page_files, temp_bucket_name, temp_prefix
                )

                # Validate and clean the extracted text
                combined_text = self.validate_and_clean_extracted_text(combined_text, local_pdf_path)

                return combined_text, textract_responses

        except Exception as e:
            logging.error(f"Error processing S3 PDF s3://{bucket_name}/{object_key}: {e}")
            raise

    def process_pdf_pages_parallel_with_responses(self, page_files: List[str], bucket_name: str, temp_prefix: str, max_workers: int = DEFAULT_MAX_WORKERS) -> Tuple[str, List[Dict]]:
        """
        Process multiple PDF pages with Textract in parallel and return both text and response objects.

        Args:
            page_files: List of paths to individual page PDF files
            bucket_name: S3 bucket name for temporary uploads
            temp_prefix: S3 prefix for temporary files
            max_workers: Maximum number of parallel workers

        Returns:
            Tuple of (combined_text, list_of_textract_responses)
        """
        if not page_files:
            logging.warning("No page files provided for processing")
            return "", []

        # Create unique temp prefix to avoid conflicts
        timestamp = int(time.time() * 1000)
        unique_temp_prefix = f"{temp_prefix}/{timestamp}"

        # Process pages in parallel
        page_results = {}
        page_responses = {}
        expected_pages = set()

        # Extract expected page numbers from filenames
        for page_file in page_files:
            filename = os.path.basename(page_file)
            if '_page_' in filename:
                page_part = filename.split('_page_')[1].split('.')[0]
                expected_pages.add(int(page_part))
            else:
                page_part = filename.split('_')[1].split('.')[0]
                expected_pages.add(int(page_part))

        logging.info(f"Expected pages: {sorted(expected_pages)}")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_page = {
                executor.submit(self.process_page_with_textract_response, page_path, bucket_name, unique_temp_prefix): page_path
                for page_path in page_files
            }

            # Collect results
            for future in concurrent.futures.as_completed(future_to_page):
                page_path = future_to_page[future]
                try:
                    page_num, page_text, response = future.result()
                    if page_num > 0:  # Valid page
                        page_results[page_num] = page_text
                        page_responses[page_num] = response
                        logging.info(f"Successfully processed page {page_num}")
                    else:
                        logging.error(f"Failed to process page from {page_path}")
                except Exception as exc:
                    logging.error(f"Page {page_path} generated an exception: {exc}")

        # Validate all expected pages were processed
        processed_pages = set(page_results.keys()) - {0}  # Remove error pages
        missing_pages = expected_pages - processed_pages
        extra_pages = processed_pages - expected_pages

        if missing_pages:
            logging.error(f"Missing pages: {sorted(missing_pages)}")
        if extra_pages:
            logging.warning(f"Extra pages: {sorted(extra_pages)}")

        # Combine results in page order
        combined_text_parts = []
        textract_responses = []
        for page_num in sorted(page_results.keys()):
            if page_num > 0:  # Skip error pages (page_num = 0)
                page_text = page_results[page_num]
                combined_text_parts.append(f"<page{page_num}>\n{page_text}\n</page{page_num}>")
                if page_num in page_responses:
                    textract_responses.append(page_responses[page_num])

        logging.info(f"Combined {len(combined_text_parts)} pages into final text")
        return '\n\n'.join(combined_text_parts), textract_responses

    def validate_and_clean_extracted_text(self, combined_text: str, pdf_path: str) -> str:
        """
        Validate and clean the extracted text to ensure data integrity.

        Args:
            combined_text: Combined text from all pages
            pdf_path: Original PDF path for validation

        Returns:
            Cleaned and validated text
        """
        pdf_name = Path(pdf_path).stem

        # Split into pages
        pages = combined_text.split('<page')
        cleaned_pages = []

        for i, page_content in enumerate(pages):
            if not page_content.strip():
                continue

            # Reconstruct page tag if it was split
            if i > 0:  # Skip first empty split
                page_content = '<page' + page_content

            # Basic text cleaning
            # Remove excessive whitespace while preserving structure
            lines = page_content.split('\n')
            cleaned_lines = []

            for line in lines:
                # Remove excessive spaces but keep single spaces
                cleaned_line = ' '.join(line.split())
                if cleaned_line:  # Only add non-empty lines
                    cleaned_lines.append(cleaned_line)

            if cleaned_lines:
                cleaned_pages.append('\n'.join(cleaned_lines))

        # Rejoin pages
        final_text = '\n\n'.join(cleaned_pages)

        # Final validation
        char_count = len(final_text)
        page_count = len(cleaned_pages)

        logging.info(f"Text validation for {pdf_name}: {char_count} characters, {page_count} pages")

        if char_count < 10:
            logging.warning(f"Very short extracted text ({char_count} chars) for {pdf_name}")

        return final_text

    @staticmethod
    def is_supported_file_type(file_path: str) -> bool:
        """
        Check if the file type is supported for processing.

        Args:
            file_path: Path to the file

        Returns:
            True if file type is supported, False otherwise
        """
        supported_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.tif', '.tiff'}
        file_ext = Path(file_path).suffix.lower()
        return file_ext in supported_extensions

    @staticmethod
    def is_pdf_file(file_path: str) -> bool:
        """
        Check if the file is a PDF.

        Args:
            file_path: Path to the file

        Returns:
            True if file is PDF, False otherwise
        """
        return Path(file_path).suffix.lower() == '.pdf'

    def process_document_classification(self, s3_uri: str, temp_bucket_name: str, temp_prefix: str) -> Tuple[str, List[Dict]]:
        """
        Process a document for classification - handles both PDFs and images from S3.

        Args:
            s3_uri: S3 URI of the document
            temp_bucket_name: S3 bucket name for temporary uploads
            temp_prefix: S3 prefix for temporary files

        Returns:
            Tuple of (combined_text, list_of_textract_responses)
        """
        # Parse S3 URI
        bucket_name, object_key = parse_s3_uri(s3_uri)

        # Validate file type
        if not self.is_supported_file_type(object_key):
            raise ValueError("S3 object must be a supported type (PDF, JPG, JPEG, PNG, TIF, TIFF)")

        textract_responses = []

        if self.is_pdf_file(object_key):
            # Process PDF from S3 - download, split, and process in parallel
            logger.info("Processing PDF from S3...")
            combined_text, textract_responses = self.process_s3_pdf_with_responses(
                bucket_name, object_key, temp_bucket_name, temp_prefix
            )
        else:
            # Process image from S3 directly
            logger.info("Processing image from S3...")
            extracted_text, textract_response = self.process_s3_image_with_textract_response(
                bucket_name, object_key
            )
            textract_responses = [textract_response]
            # For images, wrap in page1 tags to maintain consistency with PDF processing
            combined_text = f"<page1>\n{extracted_text}\n</page1>"

        return combined_text, textract_responses


async def process_document_with_textract(s3_uri: str, s3_client=None) -> Dict[str, Any]:
    """
    Process document using intelligent Textract (sync first, then async fallback).

    This function automatically determines the best processing method:
    - For supported formats (PDF, JPG, PNG, TIFF) under 10MB: tries sync first, falls back to async
    - For other files or larger files: uses async directly

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key

    Returns:
        dict: Textract processing results with processing method metadata
    """
    processor = TextractProcessor(s3_client)
    return await processor.process_document(s3_uri)



if __name__ == "__main__":
    # Configure logger to print to console
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[logging.StreamHandler()]
    )
    # Example usage
    s3_uri = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'
    asyncio.run(process_document_with_textract(s3_uri))

