# Standard library imports
import logging
import asyncio
import traceback
import atexit
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# Third-party imports
from botocore.exceptions import ClientError

# Local imports
from app.utils.textract import process_document_with_textract
from app.llm.bedrock import BedrockProcessor
from app.prompts.invoice_extraction import SYSTEM_PROMPT_EXTRACTION
from app.utils.langfuse_util import get_langfuse_util
from app.core.configuration import settings
from app.utils.text_utils import extract_json_from_backticks

# Constants
DEFAULT_TEMPERATURE = 0.2
DEFAULT_TOP_P = 0.5
DEFAULT_MAX_TOKENS = 1000
LANGFUSE_SHUTDOWN_TIMEOUT = 3.0

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # This will output to terminal/CloudWatch
    ]
)
logger = logging.getLogger(__name__)


class DocumentExtractionProcessor:
    """
    Document extraction processor for invoice data extraction.

    This class handles the complete extraction pipeline:
    - Document analysis using AWS Textract (sync/async based on file characteristics)
    - Intelligent data extraction using AWS Bedrock LLM models
    - Comprehensive error handling and logging for production environments
    - Langfuse integration for monitoring and observability

    Example:
        processor = DocumentExtractionProcessor()
        result = await processor.process_document_extraction('s3://bucket/invoice.pdf')
    """

    def __init__(self, s3_client=None):
        """
        Initialize the extraction processor with AWS credentials from settings.

        Args:
            s3_client: Optional pre-configured S3 client. If None, a new client
                      will be created using credentials from settings.

        Raises:
            Exception: If initialization fails due to missing credentials or
                      invalid configuration.
        """

        try:
            self.s3_client = s3_client
            self.temperature = settings.TEMPERATURE_EXTRACTION
            self.top_p = settings.TOP_P_EXTRACTION
            self.max_tokens = settings.MAX_TOKENS_EXTRACTION

            logger.info("DocumentExtractionProcessor initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize DocumentExtractionProcessor: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise

    def _create_extraction_metadata(self, s3_uri: str, start_time: datetime, end_time: datetime,
                                   success: bool, textract_result: Optional[Dict] = None,
                                   error: Optional[Exception] = None) -> Dict[str, Any]:
        """
        Helper method to create extraction metadata with consistent structure.

        Args:
            s3_uri: S3 URI of the processed document
            start_time: Processing start time
            end_time: Processing end time
            success: Whether processing was successful
            textract_result: Optional textract result for metadata extraction
            error: Optional error for error metadata

        Returns:
            Extraction metadata dictionary
        """
        processing_time = (end_time - start_time).total_seconds()

        metadata = {
            "s3_location": s3_uri,
            "processing_start_time": start_time.isoformat(),
            "processing_end_time": end_time.isoformat(),
            "total_processing_time_seconds": processing_time,
            "success": success
        }

        if success and textract_result:
            metadata["textract_processing_method"] = textract_result.get('textract_metadata', {}).get('processing_method', 'unknown')
        elif not success and error:
            metadata.update({
                "error_type": type(error).__name__,
                "error_message": str(error)
            })

        return metadata



    async def extract_data_with_bedrock(self,
                                       system_prompt: Optional[str] = None,
                                       user_prompt: Optional[str] = None,
                                       temperature: float = DEFAULT_TEMPERATURE,
                                       top_p: float = DEFAULT_TOP_P,
                                       max_tokens: int = DEFAULT_MAX_TOKENS) -> Dict[str, Any]:
        """
        Extract structured data using AWS Bedrock with comprehensive validation.

        This method handles the complete Bedrock interaction including:
        - Input validation and sanitization
        - Langfuse tracking for observability
        - JSON parsing with fallback mechanisms
        - Comprehensive error handling

        Args:
            system_prompt: Optional custom system prompt. If None, uses default.
            user_prompt: Optional custom user prompt containing document text.
            temperature: Model temperature for randomness control (0-2, default: 0.1).
            top_p: Model top_p for nucleus sampling (0-1, default: 0.9).
            max_tokens: Maximum tokens to generate (positive int, default: 4000).

        Returns:
            dict: Dictionary containing:
                - 'data': Extracted structured data (dict or error info)
                - 'raw_response': Complete Bedrock response object

        Raises:
            ClientError: If Bedrock API call fails
            Exception: For other unexpected errors
        """
        logger.info(f"    Temperature: {temperature}")
        logger.info(f"    Top P: {top_p}")
        logger.info(f"    Max tokens: {max_tokens}")

        # Initialize Bedrock processor with extraction settings
        bedrock_processor = BedrockProcessor(aws_region=settings.AWS_REGION_EXTRACTION)

        # Initialize Langfuse tracking
        langfuse_util = get_langfuse_util()
        generation = None

        if langfuse_util.is_enabled:
            generation = langfuse_util.create_generation(
                name="bedrock_extraction",
                model=settings.MODEL_ID_EXTRACTION,
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                model_parameters={
                    "temperature": temperature,
                    "top_p": top_p,
                    "max_tokens": max_tokens
                }
            )

        try:
            # Prepare messages and inference config
            messages = [
                {
                    "role": "user",
                    "content": [{"text": user_prompt}]
                }
            ]

            inference_config = {
                'maxTokens': max_tokens,
                'temperature': temperature,
                'topP': top_p
            }

            # Call Bedrock using helper method with extraction model
            response = bedrock_processor.call_bedrock_converse(
                model_id=settings.MODEL_ID_EXTRACTION,
                system_prompt=system_prompt or "",
                messages=messages,
                inference_config=inference_config
            )

            # Extract the content from response using helper method
            extracted_content = bedrock_processor.extract_text_from_response(response)

            # End generation with successful response
            if generation:
                usage_details = {
                    "input": response.get('usage', {}).get('inputTokens', 0),
                    "output": response.get('usage', {}).get('outputTokens', 0),
                    "total": response.get('usage', {}).get('totalTokens', 0)
                } if 'usage' in response else None

                langfuse_util.end_generation(
                    generation=generation,
                    output=extracted_content,
                    usage_details=usage_details
                )

            # Try to parse as JSON
            try:
                extracted_data = extract_json_from_backticks(extracted_content)

                # Check if extraction was successful
                if extracted_data.get('working_json', False):
                    logger.info("Successfully extracted and parsed JSON data")
                    return {
                        "data": extracted_data.get('data', {}),
                        "raw_response": response
                    }
                else:
                    logger.warning(f"JSON extraction failed: {extracted_data.get('error', 'Unknown error')}")
                    return {
                        "data": {"extracted_text": extracted_content, "parse_error": extracted_data.get('error')},
                        "raw_response": response
                    }

            except Exception as e:
                logger.warning(f"Error during JSON extraction: {str(e)}")
                return {
                    "data": {"extracted_text": extracted_content, "parse_error": str(e)},
                    "raw_response": response
                }

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"Bedrock ClientError: {error_code} - {error_message}")
            logger.debug(f"Traceback: {traceback.format_exc()}")

            # End generation with ClientError details
            if generation:
                langfuse_util.end_generation(
                    generation=generation,
                    output="",
                    level="ERROR",
                    status_message=f"Bedrock ClientError: {error_code} - {error_message}"
                )
            raise

        except Exception as e:
            logger.error(f"Unexpected error during Bedrock extraction: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")

            # End generation with general error details
            if generation:
                langfuse_util.end_generation(
                    generation=generation,
                    output="",
                    level="ERROR",
                    status_message=f"Unexpected error: {str(e)}"
                )
            raise

        finally:
            # Ensure Langfuse data is flushed
            if langfuse_util.is_enabled:
                langfuse_util.flush()



    async def process_document_extraction(self, s3_uri: str) -> Dict[str, Any]:
        """
        Execute the complete document extraction pipeline.

        This is the main entry point for document processing that orchestrates:
        1. Input validation and S3 URI parsing
        2. AWS Textract document analysis (sync/async based on file characteristics)
        3. Text extraction and structuring with coordinates
        4. AWS Bedrock LLM processing for structured data extraction
        5. Result compilation with comprehensive metadata

        Args:
            s3_uri: Valid S3 URI pointing to the document to process.
                   Must be in format 's3://bucket-name/object-key'.
                   Supported formats: PDF, JPG, JPEG, PNG, TIF, TIFF.

        Returns:
            dict: Complete extraction results containing:
                - 'extraction_metadata': Processing metadata and timing
                - 'textract_metadata': Textract processing results and metadata
                - 'bedrock_metadata': Bedrock processing metadata (if available)
                - 'structured_data': Extracted structured data from the document
                - 'raw_response': Raw Bedrock response object

        Raises:
            ValueError: If S3 URI is invalid or malformed
            Exception: For processing failures (logged with full context)
        """
        # Input validation
        if not s3_uri or not isinstance(s3_uri, str):
            raise ValueError("s3_uri must be a non-empty string")

        if not s3_uri.startswith('s3://'):
            raise ValueError("s3_uri must start with 's3://'")

        start_time = datetime.now()

        try:

            # Step 1: Analyze document with Textract
            logger.info("Step 1: Document analysis with Textract")
            textract_result = await process_document_with_textract(s3_uri, s3_client=self.s3_client)

            # Step 2: Extract text from Textract result
            logger.info("Step 2: Text extraction from Textract results")
            structured_text = textract_result.get('structured_text', '')

            if not structured_text:
                logger.warning("No structured text found in Textract result")
                raise ValueError("No structured text extracted from document")

            logger.debug(f"Extracted text length: {len(structured_text)} characters")

            # Step 3: Extract structured data
            logger.info("Step 3: Structured data extraction with Bedrock")
            bedrock_result = await self.extract_data_with_bedrock(
                system_prompt=SYSTEM_PROMPT_EXTRACTION,
                user_prompt=structured_text,
                temperature=self.temperature,
                top_p=self.top_p,
                max_tokens=self.max_tokens
            )

            if not bedrock_result:
                logger.warning("No result from Bedrock extraction")
                raise ValueError("No result from Bedrock extraction")

            # Compile final results
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Create extraction metadata using helper method
            extraction_metadata = self._create_extraction_metadata(
                s3_uri, start_time, end_time, success=True, textract_result=textract_result
            )

            # Extract components from bedrock result without validation
            bedrock_metadata = bedrock_result.get('bedrock_metadata', {}) if isinstance(bedrock_result, dict) else {}
            structured_data = bedrock_result.get('data', {}) if isinstance(bedrock_result, dict) else bedrock_result
            raw_response = bedrock_result.get('raw_response', {}) if isinstance(bedrock_result, dict) else {}

            final_result = {
                "structured_data": structured_data,
                "bedrock_response_object": raw_response,
                "execution_info": {
                    "textract": textract_result,
                    "bedrock": bedrock_metadata,
                    "extraction": extraction_metadata
                }
            }

            logger.info("Document extraction pipeline completed successfully")
            logger.info(f"Total processing time: {processing_time:.2f} seconds")

            return final_result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("Document extraction pipeline failed")
            logger.error(f"S3 URI: {s3_uri}")
            logger.error(f"Error Type: {type(e).__name__}")
            logger.error(f"Error: {str(e)}")
            logger.error(f"Processing time before failure: {processing_time:.2f} seconds")
            logger.debug(f"Traceback: {traceback.format_exc()}")

            # Create error metadata using helper method
            extraction_metadata = self._create_extraction_metadata(
                s3_uri, start_time, end_time, success=False, error=e
            )

            # Return error result instead of raising for production resilience
            error_result = {
                "structured_data": {},
                "bedrock_response_object": {},
                "execution_info": {
                    "textract": {},
                    "bedrock": {},
                    "extraction": extraction_metadata
                },
                "error": {
                    "type": type(e).__name__,
                    "message": str(e),
                    "traceback": traceback.format_exc()
                }
            }
            return error_result


# Shutdown function for Langfuse cleanup
def shutdown_langfuse():
    """Shutdown Langfuse client with timeout."""
    try:
        langfuse_util = get_langfuse_util()
        langfuse_util.shutdown(timeout=LANGFUSE_SHUTDOWN_TIMEOUT)
    except Exception as e:
        logger.error(f"Error during Langfuse shutdown: {str(e)}")


# Main function for testing and standalone execution
async def main(s3_uri: str = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf', s3_client=None):
    """
    Main function for testing the extraction pipeline.

    Args:
        s3_uri: S3 URI of the document to process
        s3_client: Optional pre-configured S3 client

    Returns:
        dict: Complete extraction results
    """
    logger.info("Starting Document Extraction Pipeline")
    logger.info(f"Input S3 URI: {s3_uri}")

    try:
        # Run the extraction
        processor = DocumentExtractionProcessor(s3_client=s3_client)
        result = await processor.process_document_extraction(s3_uri)

        # Log summary
        logger.info("EXTRACTION SUMMARY:")
        structured_data = result.get('structured_data', {})

        # Handle different response formats from Bedrock
        if isinstance(structured_data, dict):
            if 'data' in structured_data:
                # Standard format with 'data' wrapper
                data = structured_data['data']
            else:
                # Direct format
                data = structured_data

            if isinstance(data, dict):
                vendor_name = data.get('vendor_name', 'Unknown')
                invoice_number = data.get('invoice_number', 'Unknown')
                invoice_amount = data.get('invoice_amount', 'Unknown')
                logger.info(f"    Vendor: {vendor_name}")
                logger.info(f"    Invoice Number: {invoice_number}")
                logger.info(f"    Invoice Amount: {invoice_amount}")
            else:
                logger.debug(f"Structured data (non-dict): {data}")
        else:
            logger.debug(f"Raw structured data: {structured_data}")

        logger.info("Pipeline completed successfully")
        return result

    except Exception as e:
        logger.error(f"Pipeline failed with error: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        raise

    finally:
        # Ensure Langfuse is properly shutdown
        shutdown_langfuse()


if __name__ == "__main__":
    # Register atexit handler for cleanup
    atexit.register(shutdown_langfuse)

    # Example usage without giving s3_client
    s3_uri = r's3://document-extraction-logistically/temp/COUNET11522098_INV.pdf'

    # running main and saving result in json file
    import json
    import asyncio

    result1 = asyncio.run(main(s3_uri))

    print(result1["structured_data"])

    # # Example usage with s3_client
    # # Creating s3_client
    # import boto3
    # s3_client = boto3.client('s3', region_name='us-east-1')
    # s3_uri = r's3://document-extraction-logistically/temp/labelled-sample-invoice.tiff'

    # # Run the extraction pipeline
    # asyncio.run(main(s3_uri))
